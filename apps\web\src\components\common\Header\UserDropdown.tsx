'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { loggedOut, useAuth, useLazyGetLogoutUrlQuery } from '@ninebot/core'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'

import { Modal } from '@/components'

interface UserDropdownProps {
  setIsOpen: (isOpen: boolean) => void
  isLoggedIn: boolean
}

const buttonClassName = 'whitespace-nowrap hover:text-primary'

const UserDropdown = ({ setIsOpen, isLoggedIn }: UserDropdownProps) => {
  const getI18nString = useTranslations('Web')
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false)
  const dispatch = useAppDispatch()

  const [getLogoutUrl] = useLazyGetLogoutUrlQuery()
  const { redirectLoginUrl, redirectRegisterUrl } = useAuth()

  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleNavigation = (path: string) => {
    router.push(path)
    setIsOpen(false)
  }

  const handleLogout = async () => {
    try {
      const response = await getLogoutUrl({}).unwrap()

      // 创建隐藏的 iframe 来调用三方系统退出接口
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = response?.web_passport_logout_url?.url || ''
      iframe.onload = () => {
        console.log('三方系统退出接口调用成功')
        // 清理本地 token 和用户状态
        dispatch(loggedOut())
        // 移除 iframe
        document.body.removeChild(iframe)
        // 关闭下拉菜单并刷新页面回到首页
        setIsOpen(false)
        window.location.href = '/'
      }
      iframe.onerror = () => {
        console.error('三方系统退出接口调用失败')
        // 即使失败也要清理本地数据
        dispatch(loggedOut())
        document.body.removeChild(iframe)
        setIsOpen(false)
        window.location.href = '/'
      }
      document.body.appendChild(iframe)
    } catch {
      window.location.href = '/'
    }
  }

  return (
    <>
      <div className="max-h-[480px] min-w-[96px] overflow-y-auto rounded-base bg-white p-base-16 text-base shadow-[0px_0px_32px_0px_rgba(0,0,0,0.1)]">
        <div className="flex flex-col items-center justify-center gap-8">
          {isClient && isLoggedIn ? (
            <>
              <button
                className={buttonClassName}
                onClick={() => handleNavigation('/customer/account')}>
                {getI18nString('account_center')}
              </button>
              <button
                className={buttonClassName}
                onClick={() => handleNavigation('/customer/orders')}>
                {getI18nString('my_orders')}
              </button>
              <button className={buttonClassName} onClick={() => setIsLogoutModalOpen(true)}>
                {getI18nString('log_out')}
              </button>
            </>
          ) : (
            <>
              <button
                className={buttonClassName}
                onClick={() => {
                  redirectLoginUrl()
                  setIsOpen(false)
                }}>
                {getI18nString('log_in')}
              </button>
              <button
                className={buttonClassName}
                onClick={() => {
                  redirectRegisterUrl()
                  setIsOpen(false)
                }}>
                {getI18nString('register')}
              </button>
            </>
          )}
        </div>
      </div>
      {/* 退出登录确认弹窗 */}
      <Modal
        isOpen={isLogoutModalOpen}
        title="退出登录"
        okText="确定"
        cancelText="取消"
        width={400}
        onConfirm={() => {
          setIsLogoutModalOpen(false)
          handleLogout()
        }}
        onClose={() => setIsLogoutModalOpen(false)}>
        <p>确定要退出登录吗？</p>
      </Modal>
    </>
  )
}

export default UserDropdown
